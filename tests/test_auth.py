import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from app.models.user_model import OAuthProvider, User
from app.schemas.auth_schema import GoogleUserInfo
from app.services.auth_service import AuthService


@pytest.fixture
def auth_service():
    return AuthService()


@pytest.fixture
def mock_google_user():
    return GoogleUserInfo(
        id="*********",
        email="<EMAIL>",
        name="Test User",
        picture="https://example.com/picture.jpg",
        given_name="Test",
        family_name="User",
        verified_email=True,
    )


class TestAuthService:
    @pytest.mark.asyncio
    async def test_get_google_authorization_url(self, auth_service):
        """Test getting Google OAuth authorization URL"""
        url, state = auth_service.get_google_authorization_url()
        
        assert url.startswith("https://accounts.google.com/o/oauth2/auth")
        assert "client_id=" in url
        assert "redirect_uri=" in url
        assert "scope=" in url
        assert len(state) > 0

    @pytest.mark.asyncio
    async def test_create_tokens(self, auth_service):
        """Test creating JWT tokens"""
        # Create a mock user
        user = User(
            walletAddress="test_wallet",
            email="<EMAIL>",
            username="testuser",
        )
        user.id = "test_user_id"

        tokens = auth_service.create_tokens(user)
        
        assert tokens.access_token is not None
        assert tokens.refresh_token is not None
        assert tokens.token_type == "bearer"
        assert tokens.expires_in > 0

    @pytest.mark.asyncio
    async def test_verify_token(self, auth_service):
        """Test verifying JWT tokens"""
        # Create a mock user
        user = User(
            walletAddress="test_wallet",
            email="<EMAIL>",
            username="testuser",
        )
        user.id = "test_user_id"

        tokens = auth_service.create_tokens(user)
        
        # Verify access token
        payload = auth_service.verify_token(tokens.access_token)
        assert payload is not None
        assert payload["sub"] == "test_user_id"
        assert payload["email"] == "<EMAIL>"
        assert payload["username"] == "testuser"
        assert payload["type"] == "access"

        # Verify refresh token
        payload = auth_service.verify_token(tokens.refresh_token)
        assert payload is not None
        assert payload["type"] == "refresh"

    @pytest.mark.asyncio
    @patch('app.services.auth_service.AuthService.verify_google_token')
    @patch('app.services.user_service.UserService.get_user_by_email')
    @patch('app.services.user_service.UserService.get_user_by_username')
    async def test_google_signup_new_user(
        self, 
        mock_get_by_username, 
        mock_get_by_email, 
        mock_verify_google_token,
        auth_service, 
        mock_google_user
    ):
        """Test Google OAuth signup for new user"""
        # Mock the dependencies
        mock_verify_google_token.return_value = mock_google_user
        mock_get_by_email.return_value = None
        mock_get_by_username.return_value = None

        # Mock the User.insert method
        with patch.object(User, 'insert', new_callable=AsyncMock) as mock_insert:
            user, is_new_user = await auth_service.google_signup(
                code="test_code",
                state="test_state",
                username="testuser"
            )

            assert is_new_user is True
            assert user.email == "<EMAIL>"
            assert user.username == "testuser"
            assert len(user.oauthAccounts) == 1
            assert user.oauthAccounts[0].provider == OAuthProvider.GOOGLE
            assert user.oauthAccounts[0].provider_user_id == "*********"
            mock_insert.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.services.auth_service.AuthService.verify_google_token')
    @patch('app.services.user_service.UserService.get_user_by_username')
    async def test_google_signup_username_taken(
        self, 
        mock_get_by_username, 
        mock_verify_google_token,
        auth_service, 
        mock_google_user
    ):
        """Test Google OAuth signup with taken username"""
        # Mock the dependencies
        mock_verify_google_token.return_value = mock_google_user
        mock_get_by_username.return_value = User(
            walletAddress="existing_wallet",
            email="<EMAIL>",
            username="testuser",
        )

        with pytest.raises(ValueError, match="Username already taken"):
            await auth_service.google_signup(
                code="test_code",
                state="test_state",
                username="testuser"
            )

    @patch('app.services.auth_service.OAuthAccount')
    def test_create_oauth_account(self, mock_oauth_account, auth_service, mock_google_user):
        """Test creating OAuth account from Google user info"""
        # Mock the OAuthAccount constructor
        mock_instance = MagicMock()
        mock_oauth_account.return_value = mock_instance

        result = auth_service.create_oauth_account(mock_google_user)

        # Verify OAuthAccount was called with correct parameters
        mock_oauth_account.assert_called_once_with(
            provider=OAuthProvider.GOOGLE,
            provider_user_id="*********",
            provider_email="<EMAIL>",
            provider_name="Test User",
            provider_picture="https://example.com/picture.jpg",
            access_token=None,
            refresh_token=None,
            token_expires_at=None,
        )

        assert result == mock_instance
