#!/usr/bin/env python3
"""
Example usage of the Google OAuth signup functionality.

This script demonstrates how to use the Google OAuth signup feature.
Make sure to set up your environment variables before running this.
"""

import asyncio
import os
from app.services.auth_service import AuthService
from app.schemas.auth_schema import GoogleUserInfo


async def example_oauth_flow():
    """Example of how the OAuth flow works"""
    
    print("🚀 Google OAuth Signup Example")
    print("=" * 50)
    
    # Initialize the auth service
    auth_service = AuthService()
    
    # Step 1: Get Google OAuth URL
    print("\n1. Getting Google OAuth authorization URL...")
    try:
        authorization_url, state = auth_service.get_google_authorization_url()
        print(f"✅ Authorization URL: {authorization_url[:100]}...")
        print(f"✅ State: {state}")
    except Exception as e:
        print(f"❌ Error getting OAuth URL: {e}")
        return
    
    # Step 2: Create and verify tokens (example with mock user)
    print("\n2. Testing JWT token creation...")
    try:
        # Create a mock user for testing
        from app.models.user_model import User
        
        # Note: This will fail without database connection
        # but shows the token creation logic
        token_data = {
            "sub": "test_user_id",
            "email": "<EMAIL>", 
            "username": "testuser"
        }
        
        access_token = auth_service.create_access_token(token_data)
        refresh_token = auth_service.create_refresh_token(token_data)
        
        print(f"✅ Access token created: {access_token[:50]}...")
        print(f"✅ Refresh token created: {refresh_token[:50]}...")
        
        # Verify the tokens
        access_payload = auth_service.verify_token(access_token)
        refresh_payload = auth_service.verify_token(refresh_token)
        
        print(f"✅ Access token verified: {access_payload['sub']}")
        print(f"✅ Refresh token verified: {refresh_payload['sub']}")
        
    except Exception as e:
        print(f"❌ Error with tokens: {e}")
    
    # Step 3: Test OAuth account creation
    print("\n3. Testing OAuth account creation...")
    try:
        mock_google_user = GoogleUserInfo(
            id="*********",
            email="<EMAIL>",
            name="Test User",
            picture="https://example.com/picture.jpg",
            verified_email=True
        )
        
        # This will work without database connection
        # since we're not actually saving to DB
        print(f"✅ Mock Google user created: {mock_google_user.name}")
        print(f"✅ Email: {mock_google_user.email}")
        print(f"✅ Google ID: {mock_google_user.id}")
        
    except Exception as e:
        print(f"❌ Error creating OAuth account: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Example completed!")
    print("\nTo use this in production:")
    print("1. Set up Google OAuth credentials in .env")
    print("2. Configure MongoDB connection")
    print("3. Start the FastAPI server")
    print("4. Use the /api/v1/auth/google/* endpoints")


def check_environment():
    """Check if environment is properly configured"""
    print("🔍 Checking environment configuration...")
    
    required_vars = [
        "google_client_id",
        "google_client_secret", 
        "jwt_secret_key"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var.upper()) and not os.getenv(var.lower()):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file")
    else:
        print("✅ Environment variables configured")
    
    return len(missing_vars) == 0


if __name__ == "__main__":
    print("Google OAuth Signup System")
    print("=" * 50)
    
    # Check environment
    env_ok = check_environment()
    
    if not env_ok:
        print("\n📝 Create a .env file with the following variables:")
        print("google_client_id=your-google-client-id")
        print("google_client_secret=your-google-client-secret")
        print("jwt_secret_key=your-secret-key")
        print("mongodb_url=mongodb://localhost:27017")
        print("mongodb_name=peerhire")
    
    # Run the example
    asyncio.run(example_oauth_flow())
