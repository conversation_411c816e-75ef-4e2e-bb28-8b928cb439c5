# To run this project:
## Create and activate virtual env (the terminal should show "(venv)" activated). Install python compiler if not already 
python3 -m venv .venv    # On Windows use: python -m venv .venv 
source .venv/bin/activate   # On Windows use: .venv\Scripts\activate

## Install dependencies
pip install -r requirements.txt

## Export environment variables (or make a `.env` using `.env.template`)
export env=development
export database_url=postgresql://user:password@localhost:5432/mydb

## Run the server
uvicorn app.main:app --reload

## run this command only once locally to install pre commit hooks in .git: 
pre-commit install

## if you install any new dependency, use this command to write the dependency and its version in the requirements file: 
pip freeze > requirements.txt

# Different commands along with their function
make format   # Auto-format code

make lint     # Lint code

make test     # Run tests

make check    # Run all checks (CI-style)

# Some of the dev tools used in this repo with their use
| Tool         | Purpose                          |
| ------------ | -------------------------------- |
| `black`      | Code formatter                   |
| `isort`      | Import sorter                    |
| `flake8`     | Linter                           |
| `pytest`     | Test runner                      |
| `pre-commit` | Git hooks for formatting/linting |
| `Docker`     | Containerized services           |
| `Makefile`   | Dev commands automation          |

# Project Structure
project-root/
├── app/
│   ├── api/           👈 API route handlers
│   ├── core/          👈 App config, settings, constants
│   ├── db/            👈 Mongo connection logic
│   ├── models/        👈 MongoDB models (ODM or custom)
│   ├── schemas/       👈 Pydantic models for I/O
│   ├── services/      👈 Business logic, DB access
│   └── main.py        👈 App entry point
├── tests/             👈 Unit/integration tests
├── .env
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
└── Makefile


always use underscore to separate words in file names. do not use hyphen
use this command to bypass pre-commit if error is coming here: git commit -m "mmm" --no-verify 

# updated user schema

| **Field Name**            | **Type**                 | **Description**                                                       |
| ------------------------- | ------------------------ | --------------------------------------------------------------------- |
| `_id`                     | ObjectId                 | Unique MongoDB ID (Primary Key).                                      |
| `walletAddress`           | String (Indexed, Unique) | User’s blockchain wallet address, linked to IdentityContract.         |
| `email`                   | String (Unique)          | Optional email for notifications or off-chain login.                  |
| `username`                | String (Unique)          | Public-facing username.                                               |
| `profileImage`            | String (URL)             | Profile picture URL.                                                  |
| `bio`                     | String                   | Short user bio.                                                       |
| `skills`                  | Array\<String>           | List of skills (used by ProfileMatchingAI).                           |
| `portfolioLinks`          | Array\<String>           | Portfolio links (GitHub, Behance, LinkedIn, etc.).                    |
| `education`               | Array\<Object>           | List of degrees, institutions, and years.                             |
| `certifications`          | Array\<String>           | On-chain certification IDs from CertificationContract.                |
| `experience`              | Array\<Object>           | Past roles (title, company, duration, summary).                       |
| `accountType`             | Enum                     | `USER`, `ADMIN`.                                                      |
| `userType`                | Enum                     | `STUDENT`, `PROFESSIONAL`, `JUROR` (from IdentityContract).           |
| `status`                  | Enum                     | `ACTIVE`, `SUSPENDED`, `DEACTIVATED`.                                 |
| `kycVerified`             | Boolean                  | True if KYC completed (IdentityContract).                             |
| `verificationLevel`       | Number (0–5)             | Identity verification tier (IdentityContract).                        |
| `isBlacklisted`           | Boolean                  | If the user has been flagged or restricted (IdentityContract).        |
| `trustScore`              | Number (0–100)           | AI-computed trustworthiness rating.                                   |
| `reputationScore`         | Number (0–5)             | On-chain rating from ReputationContract.                              |
| `totalEarnings`           | Number                   | Total platform earnings (can be on-chain tracked).                    |
| `completedProjects`       | Number                   | Number of completed gigs.                                             |
| `openProjects`            | Number                   | Currently ongoing projects.                                           |
| `reviewsCount`            | Number                   | Total number of user reviews.                                         |
| `averageRating`           | Number (0–5)             | Avg. rating from freelancers/clients.                                 |
| `reviews`                 | Array\<Object>           | Feedback data: `{ reviewer, text, sentiment? }`.                      |
| `disputeCount`            | Number                   | Number of disputes participated in.                                   |
| `conversationHistoryId`   | String                   | Links to messages (used by DisputeResolutionAI).                      |
| `projectComplexityRating` | Number (Optional)        | Tracks complexity of assigned projects (used by PricingSuggestionAI). |
| `createdAt`               | Date                     | Account creation timestamp.                                           |
| `updatedAt`               | Date                     | Last updated timestamp.                                               |
