from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1.routes import router as api_router
from app.core.config import settings
from app.db.mongodb import close_db_connection, init_db

app = FastAPI(
    title=settings.project_name,
    debug=settings.debug,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_hosts,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Versioned API prefix
app.include_router(api_router, prefix="/api/v1")


# MongoDB connection events
@app.on_event("startup")
async def startup_db_client():
    app.mongodb_client = await init_db()


@app.on_event("shutdown")
async def shutdown_db_client():
    await close_db_connection(app.mongodb_client)


@app.get("/", tags=["Health"])
async def health_check():
    """
    Root health check route.
    """
    return {"message": "PeerHire API is up and running 🚀"}
