from typing import List, Optional, Union

from pydantic import AnyUrl, field_validator
from pydantic_settings import BaseSettings


class MongoDBDsn(AnyUrl):
    allowed_schemes = {"mongodb", "mongodb+srv"}


class Settings(BaseSettings):
    # General
    env: str = "development"  # "development", "staging", "production"
    debug: bool = True

    # MongoDB Settings
    mongodb_url: MongoDBDsn
    mongodb_name: str = "peerhire"

    # CORS
    allowed_hosts: List[str] = ["http://localhost:8000"]

    project_name: str = "PeerHire Backend"

    # Add this validator to handle comma-separated string values
    @field_validator("allowed_hosts", mode="before")
    @classmethod
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v

    model_config = {"env_file": ".env"}


settings = Settings()
