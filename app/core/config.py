from typing import List

from pydantic import AnyUrl, field_validator
from pydantic_settings import BaseSettings


class MongoDBDsn(AnyUrl):
    allowed_schemes = {"mongodb", "mongodb+srv"}


class Settings(BaseSettings):
    # General
    env: str = "development"  # "development", "staging", "production"
    debug: bool = True

    # MongoDB Settings
    mongodb_url: MongoDBDsn
    mongodb_name: str = "peerhire"

    # CORS
    allowed_hosts: List[str] = ["http://localhost:8000"]

    project_name: str = "PeerHire Backend"

    # JWT Settings
    jwt_secret_key: str = "your-secret-key-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7

    # Google OAuth Settings
    google_client_id: str = ""
    google_client_secret: str = ""
    google_redirect_uri: str = "http://localhost:8000/api/v1/auth/google/callback"

    # Add this validator to handle comma-separated string values
    @field_validator("allowed_hosts", mode="before")
    @classmethod
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v

    model_config = {"env_file": ".env"}


settings = Settings()
