from beanie import init_beanie
from motor.motor_asyncio import AsyncIOMotorClient

from app.core.config import settings
from app.db.index import create_indexes
from app.models.user_model import OAuthAccount, User

# Import any other document models here


async def init_db():
    """Initialize database connection and register document models with <PERSON><PERSON>"""
    client = AsyncIOMotorClient(str(settings.mongodb_url))

    # Initialize beanie with the document models
    await init_beanie(
        database=client[settings.mongodb_name],
        document_models=[
            User,
            OAuthAccount,
            # Add other document models here
        ],
    )

    # await create_indexes()

    print("Connected to MongoDB with <PERSON><PERSON>")
    return client


async def close_db_connection(client):
    """Close the database connection"""
    client.close()
    print("Closed MongoDB connection")
