from app.models.user_model import User


async def create_indexes():
    """Create database indexes for better query performance"""

    # Create text search indexes for user search
    await User.create_indexes(
        [
            {"key": [("username", "text"), ("bio", "text"), ("skills", "text")]},
            {"key": [("walletAddress", 1)]},
            {"key": [("createdAt", -1)]},
        ]
    )

    # Additional indexes can be added here
