from datetime import datetime, timezone
from enum import Enum
from typing import List, Optional

from beanie import Document
from pydantic import EmailStr, Field, HttpUrl


class AccountType(str, Enum):
    USER = "USER"
    ADMIN = "ADMIN"


class UserType(str, Enum):
    STUDENT = "STUDENT"
    PROFESSIONAL = "PROFESSIONAL"
    JUROR = "JUROR"


class UserStatus(str, Enum):
    ACTIVE = "ACTIVE"
    SUSPENDED = "SUSPENDED"
    DEACTIVATED = "DEACTIVATED"


class OAuthProvider(str, Enum):
    GOOGLE = "GOOGLE"
    GITHUB = "GITHUB"
    LINKEDIN = "LINKEDIN"


class OAuthAccount(Document):
    """OAuth account information for external providers"""
    provider: OAuthProvider
    provider_user_id: str  # The user ID from the OAuth provider
    provider_email: str
    provider_name: Optional[str] = None
    provider_picture: Optional[str] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expires_at: Optional[datetime] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class Education(Document):
    degree: str
    institution: str
    year: int


class Experience(Document):
    title: str
    company: str
    duration: str
    summary: str


class Review(Document):
    reviewer: str
    text: str
    sentiment: Optional[str] = None


class User(Document):
    walletAddress: str = Field(index=True, unique=True)
    email: Optional[EmailStr] = Field(None, index=True, unique=True)
    username: str = Field(index=True, unique=True)
    profileImage: Optional[HttpUrl] = None
    bio: Optional[str] = None
    skills: List[str] = []
    portfolioLinks: List[str] = []
    education: List[Education] = []
    certifications: List[str] = []
    experience: List[Experience] = []
    accountType: AccountType = AccountType.USER
    userType: UserType = UserType.STUDENT
    status: UserStatus = UserStatus.ACTIVE
    kycVerified: bool = False
    verificationLevel: int = Field(0, ge=0, le=5)
    isBlacklisted: bool = False
    trustScore: Optional[float] = Field(None, ge=0, le=100)
    reputationScore: Optional[float] = Field(None, ge=0, le=5)
    totalEarnings: float = 0
    completedProjects: int = 0
    openProjects: int = 0
    reviewsCount: int = 0
    averageRating: Optional[float] = Field(None, ge=0, le=5)
    reviews: List[Review] = []
    disputeCount: int = 0
    conversationHistoryId: Optional[str] = None
    projectComplexityRating: Optional[float] = None
    # OAuth accounts linked to this user
    oauthAccounts: List[OAuthAccount] = []
    createdAt: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updatedAt: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Configure collection name in MongoDB
    class Settings:
        name = "users"
        use_state_management = True

    # Method to update timestamp on update operations
    async def before_save(self):
        self.updatedAt = datetime.now(timezone.utc)
