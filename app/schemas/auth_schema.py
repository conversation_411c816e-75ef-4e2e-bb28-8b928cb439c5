from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, HttpUrl

from app.models.user_model import OAuthProvider


class GoogleUserInfo(BaseModel):
    """Schema for Google user information from OAuth"""
    id: str
    email: EmailStr
    name: str
    picture: Optional[HttpUrl] = None
    given_name: Optional[str] = None
    family_name: Optional[str] = None
    verified_email: bool = False


class OAuthSignupRequest(BaseModel):
    """Schema for OAuth signup request"""
    provider: OAuthProvider
    code: str  # Authorization code from OAuth provider
    state: Optional[str] = None  # CSRF protection state
    username: str  # User-chosen username for the platform


class OAuthLoginRequest(BaseModel):
    """Schema for OAuth login request"""
    provider: OAuthProvider
    code: str  # Authorization code from OAuth provider
    state: Optional[str] = None  # CSRF protection state


class TokenResponse(BaseModel):
    """Schema for token response"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class AuthResponse(BaseModel):
    """Schema for authentication response"""
    user_id: str
    email: EmailStr
    username: str
    tokens: TokenResponse
    is_new_user: bool = False


class GoogleOAuthURL(BaseModel):
    """Schema for Google OAuth URL response"""
    authorization_url: str
    state: str


class RefreshTokenRequest(BaseModel):
    """Schema for refresh token request"""
    refresh_token: str


class TokenPayload(BaseModel):
    """Schema for JWT token payload"""
    sub: str  # User ID
    email: str
    username: str
    exp: datetime
    iat: datetime
    type: str  # "access" or "refresh"
