from datetime import datetime
from typing import Any, Dict, List, Optional

from beanie.exceptions import DocumentNotFound

from app.models.user_model import Education, Experience, Review, User
from app.schemas.user_schema import UserCreate, UserUpdate


class UserService:
    async def create_user(self, user_data: UserCreate) -> User:
        """Create a new user"""
        # Check if user with the same wallet address exists
        existing_user = await User.find_one(
            User.walletAddress == user_data.walletAddress
        )
        if existing_user:
            raise ValueError("User with this wallet address already exists")

        # Check if email is already registered
        if user_data.email:
            existing_email = await User.find_one(User.email == user_data.email)
            if existing_email:
                raise ValueError("User with this email already exists")

        # Check if username is already taken
        existing_username = await User.find_one(User.username == user_data.username)
        if existing_username:
            raise ValueError("Username already taken")

        # Convert education, experience and reviews objects
        education_list = [Education(**edu.model_dump()) for edu in user_data.education]
        experience_list = [
            Experience(**exp.model_dump()) for exp in user_data.experience
        ]

        # Create the user
        user = User(
            **user_data.model_dump(exclude={"education", "experience"}),
            education=education_list,
            experience=experience_list,
            reviews=[],
            createdAt=datetime.utcnow(),
            updatedAt=datetime.utcnow(),
        )

        # Save the user to database
        await user.insert()

        return user

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            return await User.get(user_id)
        except DocumentNotFound:
            return None

    async def get_user_by_wallet_address(self, wallet_address: str) -> Optional[User]:
        """Get user by wallet address"""
        return await User.find_one(User.walletAddress == wallet_address)

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return await User.find_one(User.email == email)

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        return await User.find_one(User.username == username)

    async def update_user(
        self, user_id: str, user_update: UserUpdate
    ) -> Optional[User]:
        """Update a user"""
        try:
            user = await User.get(user_id)

            # Get data to update (filter out None values)
            update_data = user_update.model_dump(exclude_unset=True)

            # Handle nested objects
            if "education" in update_data:
                education_list = [
                    Education(**edu.model_dump()) for edu in update_data["education"]
                ]
                update_data["education"] = education_list

            if "experience" in update_data:
                experience_list = [
                    Experience(**exp.model_dump()) for exp in update_data["experience"]
                ]
                update_data["experience"] = experience_list

            # Update the user with new values
            for key, value in update_data.items():
                setattr(user, key, value)

            await user.save()
            return user

        except DocumentNotFound:
            return None

    async def delete_user(self, user_id: str) -> bool:
        """Delete a user"""
        try:
            user = await User.get(user_id)
            await user.delete()
            return True
        except DocumentNotFound:
            return False

    async def list_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get a list of users"""
        return await User.find_all().skip(skip).limit(limit).to_list()

    async def add_review(
        self, user_id: str, reviewer: str, text: str, sentiment: Optional[str] = None
    ) -> Optional[User]:
        """Add a review to a user"""
        try:
            user = await User.get(user_id)

            # Create new review
            review = Review(reviewer=reviewer, text=text, sentiment=sentiment)
            user.reviews.append(review)
            user.reviewsCount += 1

            # Update updatedAt timestamp
            user.updatedAt = datetime.utcnow()

            await user.save()
            return user
        except DocumentNotFound:
            return None

    async def update_wallet_status(
        self, user_id: str, kycVerified: bool, verificationLevel: int
    ) -> Optional[User]:
        """Update wallet verification status"""
        try:
            user = await User.get(user_id)
            user.kycVerified = kycVerified
            user.verificationLevel = verificationLevel
            user.updatedAt = datetime.utcnow()

            await user.save()
            return user
        except DocumentNotFound:
            return None

    async def update_reputation(
        self, user_id: str, reputationScore: float
    ) -> Optional[User]:
        """Update user reputation score"""
        try:
            user = await User.get(user_id)
            user.reputationScore = reputationScore
            user.updatedAt = datetime.utcnow()

            await user.save()
            return user
        except DocumentNotFound:
            return None
