# Combines all v1 routers

from fastapi import APIRouter

from app.api.v1.endpoints.auth_endpoints import router as auth_router
from app.api.v1.endpoints.user_endpoints import router as users_router

router = APIRouter()

router.include_router(auth_router, prefix="/auth", tags=["Authentication"])
router.include_router(users_router, prefix="/users", tags=["Users"])


@router.get("/")
def root():
    return {"message": "Welcome to PeerHire API v1"}
