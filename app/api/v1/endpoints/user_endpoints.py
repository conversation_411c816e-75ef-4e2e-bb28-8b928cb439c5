# Keep routes thin — just validate, call service layer, return.

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import ValidationError

from app.models.user_model import User
from app.schemas.user_schema import UserCreate, UserResponse, UserUpdate
from app.services.user_service import UserService

router = APIRouter()


def get_user_service():
    return UserService()


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate, user_service: UserService = Depends(get_user_service)
):
    try:
        created_user = await user_service.create_user(user)
        return created_user
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(user_id: str, user_service: UserService = Depends(get_user_service)):
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    return user


@router.get("/wallet/{wallet_address}", response_model=UserResponse)
async def get_user_by_wallet(
    wallet_address: str, user_service: UserService = Depends(get_user_service)
):
    user = await user_service.get_user_by_wallet_address(wallet_address)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    return user


@router.get("/username/{username}", response_model=UserResponse)
async def get_user_by_username(
    username: str, user_service: UserService = Depends(get_user_service)
):
    user = await user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    return user


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    user_service: UserService = Depends(get_user_service),
):
    try:
        user = await user_service.update_user(user_id, user_update)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        return user
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: str, user_service: UserService = Depends(get_user_service)
):
    deleted = await user_service.delete_user(user_id)
    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )


@router.get("/", response_model=List[UserResponse])
async def list_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    user_service: UserService = Depends(get_user_service),
):
    users = await user_service.list_users(skip, limit)
    return users


@router.post("/{user_id}/reviews", response_model=UserResponse)
async def add_review(
    user_id: str,
    reviewer: str,
    text: str,
    sentiment: Optional[str] = None,
    user_service: UserService = Depends(get_user_service),
):
    user = await user_service.add_review(user_id, reviewer, text, sentiment)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    return user


@router.put("/{user_id}/kyc", response_model=UserResponse)
async def update_kyc_status(
    user_id: str,
    kyc_verified: bool,
    verification_level: int,
    user_service: UserService = Depends(get_user_service),
):
    user = await user_service.update_wallet_status(
        user_id, kycVerified=kyc_verified, verificationLevel=verification_level
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    return user
