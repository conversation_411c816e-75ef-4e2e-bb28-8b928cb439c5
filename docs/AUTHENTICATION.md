# Authentication System

This document describes the Google OAuth authentication system implemented in the PeerHire backend.

## Overview

The authentication system supports:
- Google OAuth 2.0 signup and login
- JWT token-based authentication
- Refresh token mechanism
- User account linking

## Setup

### 1. Google OAuth Configuration

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Create OAuth 2.0 credentials:
   - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:8000/api/v1/auth/google/callback` (for development)
     - Your production callback URL
5. Copy the Client ID and Client Secret

### 2. Environment Variables

Add the following to your `.env` file:

```env
# JWT Settings
jwt_secret_key=your-super-secret-jwt-key-change-in-production
jwt_algorithm=HS256
jwt_access_token_expire_minutes=30
jwt_refresh_token_expire_days=7

# Google OAuth Settings
google_client_id=your-google-client-id
google_client_secret=your-google-client-secret
google_redirect_uri=http://localhost:8000/api/v1/auth/google/callback
```

## API Endpoints

### 1. Get Google OAuth URL

```http
GET /api/v1/auth/google/url
```

**Response:**
```json
{
  "authorization_url": "https://accounts.google.com/o/oauth2/auth?...",
  "state": "random-state-string"
}
```

### 2. Google OAuth Signup

```http
POST /api/v1/auth/google/signup
```

**Request Body:**
```json
{
  "provider": "GOOGLE",
  "code": "authorization-code-from-google",
  "state": "state-from-step-1",
  "username": "desired-username"
}
```

**Response:**
```json
{
  "user_id": "user-id",
  "email": "<EMAIL>",
  "username": "username",
  "tokens": {
    "access_token": "jwt-access-token",
    "refresh_token": "jwt-refresh-token",
    "token_type": "bearer",
    "expires_in": 1800
  },
  "is_new_user": true
}
```

### 3. Google OAuth Login

```http
POST /api/v1/auth/google/login
```

**Request Body:**
```json
{
  "provider": "GOOGLE",
  "code": "authorization-code-from-google",
  "state": "state-from-step-1"
}
```

**Response:**
```json
{
  "user_id": "user-id",
  "email": "<EMAIL>",
  "username": "username",
  "tokens": {
    "access_token": "jwt-access-token",
    "refresh_token": "jwt-refresh-token",
    "token_type": "bearer",
    "expires_in": 1800
  },
  "is_new_user": false
}
```

### 4. Refresh Token

```http
POST /api/v1/auth/refresh
```

**Request Body:**
```json
{
  "refresh_token": "jwt-refresh-token"
}
```

**Response:**
```json
{
  "access_token": "new-jwt-access-token",
  "refresh_token": "new-jwt-refresh-token",
  "token_type": "bearer",
  "expires_in": 1800
}
```

## Authentication Flow

### For Web Applications

1. **Get OAuth URL**: Call `GET /api/v1/auth/google/url` to get the Google OAuth URL
2. **Redirect User**: Redirect user to the authorization URL
3. **Handle Callback**: Google redirects back with `code` and `state`
4. **Complete Signup/Login**: Use the `code` and `state` to call signup or login endpoint

### For Mobile/SPA Applications

1. **Get OAuth URL**: Call `GET /api/v1/auth/google/url` to get the Google OAuth URL
2. **Open OAuth Flow**: Open the URL in a web view or browser
3. **Extract Code**: Extract the authorization code from the callback URL
4. **Complete Signup/Login**: Use the code to call signup or login endpoint

## Protected Routes

To protect routes with authentication, use the `get_current_user` dependency:

```python
from fastapi import Depends
from app.core.dependencies import get_current_user
from app.models.user_model import User

@router.get("/protected")
async def protected_route(current_user: User = Depends(get_current_user)):
    return {"message": f"Hello {current_user.username}"}
```

## Error Handling

The authentication system returns appropriate HTTP status codes:

- `400 Bad Request`: Invalid request data or OAuth errors
- `401 Unauthorized`: Invalid or expired tokens
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: User not found during login
- `422 Unprocessable Entity`: Validation errors
- `500 Internal Server Error`: Server-side errors

## Security Considerations

1. **JWT Secret**: Use a strong, random secret key in production
2. **HTTPS**: Always use HTTPS in production
3. **Token Expiration**: Access tokens expire in 30 minutes by default
4. **Refresh Tokens**: Refresh tokens expire in 7 days by default
5. **State Parameter**: Used for CSRF protection in OAuth flow
