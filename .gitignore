# Python
__pycache__/
*.py[cod]
*.so
*.pyd
*.pyo
*.egg
*.egg-info/
*.log

# Virtual environments
venv/
.venv/

# Environment variables
.env
.env.*

# VS Code & IDEs
.vscode/
.idea/
*.swp
*~

# Byte-compiled / Cache / Coverage
*.pyc
.pytest_cache/
.cache/
.coverage
htmlcov/

# Mypy / Linting
.mypy_cache/
.dmypy.json
.pyre/
.pylint.d/

# Testing / Coverage / Profiling
.tox/
.nox/
nosetests.xml
coverage.xml
*.cover
*.py,cover

# Jupyter / IPython
.ipynb_checkpoints/
profile_default/
ipython_config.py

# Docker
*.pid
*.tar
*.bak
Dockerfile.*
docker-compose.override.yml

# Build / Dist
build/
dist/
*.whl
*.egg-info/

# Logs / Dumps
logs/
*.log
*.out
*.err
*.pid

# SQL / SQLite
*.sqlite3
*.db

# Node (if using frontend in same repo)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Mac / OS
.DS_Store
Thumbs.db
ehthumbs.db
Icon?

# Pre-commit
.pre-commit/

# JetBrains
.idea/

# Misc
*.bak
*.tmp
